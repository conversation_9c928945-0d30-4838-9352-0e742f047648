using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace CalculatorSystem
{
    /// <summary>
    /// 流式处理器，专门处理大数据量计算，避免内存爆满
    /// </summary>
    public class StreamingProcessor
    {
        private readonly Action<string, int> statusUpdater;
        private const int BATCH_SIZE = 10000;
        private const int MEMORY_CHECK_INTERVAL = 50000;

        public StreamingProcessor(Action<string, int> statusUpdater)
        {
            this.statusUpdater = statusUpdater;
        }

        /// <summary>
        /// 流式生成磁控点并直接写入文件
        /// </summary>
        public async Task<string> GenerateMagneticPointsToFile(
            List<string> validTwoCombinations,
            int magneticPointCount,
            int maxNumber,
            string outputFilePath,
            CancellationToken cancellationToken)
        {
            var tempFilePath = Path.GetTempFileName();
            var processedCount = 0;
            var totalCombinations = validTwoCombinations.Count;

            try
            {
                using (var writer = new StreamWriter(tempFilePath, false, Encoding.UTF8))
                {
                    var allNumbers = new int[maxNumber];
                    for (int i = 0; i < maxNumber; i++)
                    {
                        allNumbers[i] = i + 1;
                    }

                    var remainingNumbers = new int[maxNumber - 2];
                    var fullCombination = new int[magneticPointCount];
                    var sb = new StringBuilder(magneticPointCount * 3);
                    int needCount = magneticPointCount - 2;

                    foreach (string twoCombination in validTwoCombinations)
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        // 解析2数组合
                        int dashIndex = twoCombination.IndexOf('-');
                        int num1 = int.Parse(twoCombination.Substring(0, dashIndex));
                        int num2 = int.Parse(twoCombination.Substring(dashIndex + 1));

                        // 构建剩余数字数组
                        int remainingCount = 0;
                        for (int i = 0; i < maxNumber; i++)
                        {
                            int number = allNumbers[i];
                            if (number != num1 && number != num2)
                            {
                                remainingNumbers[remainingCount++] = number;
                            }
                        }

                        // 生成组合并直接写入文件
                        await WriteCombinatonsToFile(writer, remainingNumbers, remainingCount, 
                            needCount, num1, num2, fullCombination, sb, magneticPointCount);

                        processedCount++;

                        // 定期更新进度和内存检查
                        if (processedCount % Math.Max(1, totalCombinations / 100) == 0)
                        {
                            int progress = (int)((double)processedCount / totalCombinations * 100);
                            statusUpdater?.Invoke($"流式处理磁控点... ({processedCount}/{totalCombinations})", progress);
                            
                            await Task.Delay(1, cancellationToken);
                        }

                        if (processedCount % MEMORY_CHECK_INTERVAL == 0)
                        {
                            GC.Collect(0, GCCollectionMode.Optimized);
                        }
                    }
                }

                // 移动临时文件到目标位置
                if (File.Exists(outputFilePath))
                {
                    File.Delete(outputFilePath);
                }
                File.Move(tempFilePath, outputFilePath);

                return outputFilePath;
            }
            catch
            {
                // 清理临时文件
                if (File.Exists(tempFilePath))
                {
                    File.Delete(tempFilePath);
                }
                throw;
            }
        }

        /// <summary>
        /// 将组合写入文件
        /// </summary>
        private async Task WriteCombinatonsToFile(StreamWriter writer, int[] remainingNumbers, 
            int remainingCount, int needCount, int num1, int num2, int[] fullCombination, 
            StringBuilder sb, int magneticPointCount)
        {
            var combinations = GetCombinationsOptimized(remainingNumbers, remainingCount, needCount);

            foreach (var combination in combinations)
            {
                // 构建完整组合
                fullCombination[0] = num1;
                fullCombination[1] = num2;
                Array.Copy(combination, 0, fullCombination, 2, needCount);
                Array.Sort(fullCombination);

                // 构建字符串
                sb.Clear();
                for (int i = 0; i < magneticPointCount; i++)
                {
                    if (i > 0) sb.Append('-');
                    sb.Append(fullCombination[i]);
                }

                await writer.WriteLineAsync(sb.ToString());
            }
        }

        /// <summary>
        /// 优化的组合生成器
        /// </summary>
        private IEnumerable<int[]> GetCombinationsOptimized(int[] array, int arrayLength, int length)
        {
            if (length == 0)
            {
                yield return new int[0];
                yield break;
            }

            if (length == 1)
            {
                for (int i = 0; i < arrayLength; i++)
                {
                    yield return new int[] { array[i] };
                }
                yield break;
            }

            var indices = new int[length];
            for (int i = 0; i < length; i++)
            {
                indices[i] = i;
            }

            var result = new int[length];
            do
            {
                for (int i = 0; i < length; i++)
                {
                    result[i] = array[indices[i]];
                }

                var combination = new int[length];
                Array.Copy(result, combination, length);
                yield return combination;

            } while (NextCombination(indices, arrayLength, length));
        }

        /// <summary>
        /// 生成下一个组合的索引
        /// </summary>
        private bool NextCombination(int[] indices, int n, int k)
        {
            for (int i = k - 1; i >= 0; i--)
            {
                if (indices[i] < n - k + i)
                {
                    indices[i]++;
                    for (int j = i + 1; j < k; j++)
                    {
                        indices[j] = indices[j - 1] + 1;
                    }
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 从文件统计磁控点频次
        /// </summary>
        public async Task<Dictionary<string, int>> CountMagneticPointsFromFile(
            string filePath, 
            CancellationToken cancellationToken)
        {
            var counts = new Dictionary<string, int>();
            var processedLines = 0;

            using (var reader = new StreamReader(filePath, Encoding.UTF8))
            {
                string line;
                while ((line = await reader.ReadLineAsync()) != null)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    line = line.Trim();
                    if (!string.IsNullOrEmpty(line))
                    {
                        if (counts.TryGetValue(line, out int count))
                        {
                            counts[line] = count + 1;
                        }
                        else
                        {
                            counts[line] = 1;
                        }
                    }

                    processedLines++;

                    if (processedLines % BATCH_SIZE == 0)
                    {
                        statusUpdater?.Invoke($"统计频次... ({processedLines} 行)", -1);
                        await Task.Delay(1, cancellationToken);

                        if (processedLines % MEMORY_CHECK_INTERVAL == 0)
                        {
                            GC.Collect(0, GCCollectionMode.Optimized);
                        }
                    }
                }
            }

            return counts;
        }
    }
}
