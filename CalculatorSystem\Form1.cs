using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Threading;

namespace CalculatorSystem
{
    public partial class Form1 : Form
    {
        private CancellationTokenSource cancellationTokenSource;
        private List<string> originalData;
        private bool isProcessing = false;

        // 两阶段操作相关字段
        private Dictionary<int, List<string>> frequencyCombinations; // 频次 -> 组合列表
        private Dictionary<string, int> twoCombinationCounts; // 组合 -> 频次
        private HashSet<string> selectedCombinations; // 保存跨频次选择的组合

        // 缓存相关字段
        private Dictionary<string, Dictionary<string, int>> twoCombinationCache; // 文件路径 -> 2数组合缓存
        private Dictionary<string, Dictionary<string, int>> magneticPointCache; // 参数组合 -> 磁控点缓存
        private Dictionary<string, List<string>> filteredResultsCache; // 筛选参数 -> 结果缓存
        private string lastLoadedFilePath; // 上次加载的文件路径
        private string lastFileHash; // 上次加载文件的哈希值

        public Form1()
        {
            InitializeComponent();
            InitializeForm();
        }

        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void InitializeForm()
        {
            originalData = new List<string>();
            frequencyCombinations = new Dictionary<int, List<string>>();
            twoCombinationCounts = new Dictionary<string, int>();
            selectedCombinations = new HashSet<string>();

            // 初始化缓存
            InitializeCache();

            // 设置默认值
            numMaxNumber.Value = 33;
            numMagneticPoints.Value = 5;
           // numTwoCombinationMin.Value = 5;
           // numTwoCombinationMax.Value = 50;
            numResultMin.Value = 10;
            numResultMax.Value = 15;

            // 设置进度条
            progressBar.Minimum = 0;
            progressBar.Maximum = 100;
            progressBar.Value = 0;

            // 初始化ListView
            InitializeListViews();

            UpdateUI();
        }

        /// <summary>
        /// 初始化缓存系统
        /// </summary>
        private void InitializeCache()
        {
            twoCombinationCache = new Dictionary<string, Dictionary<string, int>>();
            magneticPointCache = new Dictionary<string, Dictionary<string, int>>();
            filteredResultsCache = new Dictionary<string, List<string>>();
            lastLoadedFilePath = string.Empty;
            lastFileHash = string.Empty;
        }

        /// <summary>
        /// 计算文件哈希值，用于检测文件变化
        /// </summary>
        private string CalculateFileHash(string filePath)
        {
            try
            {
                using (var md5 = System.Security.Cryptography.MD5.Create())
                {
                    using (var stream = File.OpenRead(filePath))
                    {
                        var hash = md5.ComputeHash(stream);
                        return Convert.ToBase64String(hash);
                    }
                }
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// 生成缓存键
        /// </summary>
        private string GenerateCacheKey(params object[] parameters)
        {
            return string.Join("|", parameters.Select(p => p?.ToString() ?? "null"));
        }

        /// <summary>
        /// 清理缓存
        /// </summary>
        private void ClearCache()
        {
            twoCombinationCache.Clear();
            magneticPointCache.Clear();
            filteredResultsCache.Clear();

            // 强制垃圾回收，释放缓存占用的内存
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
        }

        /// <summary>
        /// 检查缓存大小，必要时清理
        /// </summary>
        private void CheckCacheSize()
        {
            const int maxCacheEntries = 100; // 最大缓存条目数

            if (twoCombinationCache.Count > maxCacheEntries)
            {
                // 保留最近使用的缓存，清理旧的
                var oldestKeys = twoCombinationCache.Keys.Take(twoCombinationCache.Count - maxCacheEntries).ToList();
                foreach (var key in oldestKeys)
                {
                    twoCombinationCache.Remove(key);
                }
            }

            if (magneticPointCache.Count > maxCacheEntries)
            {
                var oldestKeys = magneticPointCache.Keys.Take(magneticPointCache.Count - maxCacheEntries).ToList();
                foreach (var key in oldestKeys)
                {
                    magneticPointCache.Remove(key);
                }
            }

            if (filteredResultsCache.Count > maxCacheEntries)
            {
                var oldestKeys = filteredResultsCache.Keys.Take(filteredResultsCache.Count - maxCacheEntries).ToList();
                foreach (var key in oldestKeys)
                {
                    filteredResultsCache.Remove(key);
                }
            }
        }

        /// <summary>
        /// 初始化ListView控件
        /// </summary>
        private void InitializeListViews()
        {
            // 初始化频次列表
            listViewFrequency.Columns.Add("频次", 80);
            listViewFrequency.Columns.Add("组合数", 100);
            listViewFrequency.SelectedIndexChanged += ListViewFrequency_SelectedIndexChanged;

            // 初始化组合列表
            listViewCombinations.Columns.Add("序号", 60);
            listViewCombinations.Columns.Add("组合", 120);
            listViewCombinations.Columns.Add("频次", 80);
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;
            listViewCombinations.CheckBoxes = true;
        }

        /// <summary>
        /// 更新界面状态
        /// </summary>
        private void UpdateUI()
        {
            btnStart.Enabled = !isProcessing && !string.IsNullOrEmpty(txtFilePath.Text);
            btnCancel.Enabled = isProcessing;

            groupBoxSettings.Enabled = !isProcessing;
            groupBoxFile.Enabled = !isProcessing;

            // 始终显示两阶段分析界面
            groupBoxResults.Visible = false;
            groupBoxTwoStage.Visible = true;
            btnStart.Text = "分析2数组合";

            // 更新导出按钮状态
            UpdateExportButtonState();

            // 按钮状态控制
            btnSelectAll.Enabled = !isProcessing && listViewCombinations.Items.Count > 0;
            btnClearSelection.Enabled = !isProcessing && selectedCombinations.Count > 0;
        }

        /// <summary>
        /// 更新导出按钮状态（数据结构优化版本）
        /// </summary>
        private void UpdateExportButtonState()
        {
            int selectedCount = selectedCombinations.Count; // 直接使用Count属性，避免方法调用
            btnExportSelected.Enabled = selectedCount > 0 && !isProcessing;

            if (selectedCount > 0)
            {
                // 使用字典统计频次分布，避免LINQ的开销
                var frequencyCountMap = new Dictionary<int, int>();

                foreach (string combination in selectedCombinations)
                {
                    if (twoCombinationCounts.TryGetValue(combination, out int frequency))
                    {
                        if (frequencyCountMap.TryGetValue(frequency, out int count))
                        {
                            frequencyCountMap[frequency] = count + 1;
                        }
                        else
                        {
                            frequencyCountMap[frequency] = 1;
                        }
                    }
                }

                // 使用StringBuilder构建统计文本，避免字符串拼接
                var sb = new StringBuilder();
                var frequencies = frequencyCountMap.Keys.ToArray();
                Array.Sort(frequencies);

                for (int i = 0; i < frequencies.Length; i++)
                {
                    if (i > 0) sb.Append(", ");
                    int freq = frequencies[i];
                    sb.Append($"频次{freq}({frequencyCountMap[freq]}个)");
                }

                lblSelectedCount.Text = $"已选择: {selectedCount} 个组合";
            }
            else
            {
                lblSelectedCount.Text = "已选择: 0 个组合";
            }
        }

        /// <summary>
        /// 获取选中的组合数量
        /// </summary>
        private int GetSelectedCombinationsCount()
        {
            return selectedCombinations.Count;
        }

        /// <summary>
        /// 频次列表选择变化事件
        /// </summary>
        private void ListViewFrequency_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listViewFrequency.SelectedItems.Count > 0)
            {
                ShowCombinationsForSelectedFrequencies();
            }
        }

        /// <summary>
        /// 组合列表选择变化事件
        /// </summary>
        private void ListViewCombinations_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            // 更新全局选择状态
            var itemData = (dynamic)e.Item.Tag;
            string combination = itemData.Combination;

            if (e.Item.Checked)
            {
                selectedCombinations.Add(combination);
            }
            else
            {
                selectedCombinations.Remove(combination);
            }

            UpdateExportButtonState();
        }

        /// <summary>
        /// 选择文件按钮点击事件
        /// </summary>
        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                txtFilePath.Text = openFileDialog.FileName;
                LoadDataFile();
                UpdateUI();
            }
        }

        /// <summary>
        /// 加载数据文件（带缓存的内存优化版本）
        /// </summary>
        private void LoadDataFile()
        {
            try
            {
                string currentFilePath = txtFilePath.Text;
                string currentFileHash = CalculateFileHash(currentFilePath);

                // 检查文件是否发生变化
                bool fileChanged = lastLoadedFilePath != currentFilePath || lastFileHash != currentFileHash;

                if (fileChanged)
                {
                    // 文件发生变化，清理相关缓存
                    ClearCache();
                    lastLoadedFilePath = currentFilePath;
                    lastFileHash = currentFileHash;
                }

                originalData.Clear();

                // 使用流式读取，避免一次性加载整个文件到内存
                int validLines = 0;
                int lineNumber = 0;

                using (var reader = new StreamReader(currentFilePath, Encoding.UTF8))
                {
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        lineNumber++;
                        line = line.Trim();

                        if (!string.IsNullOrEmpty(line))
                        {
                            if (ValidateDataLineOptimized(line, lineNumber))
                            {
                                originalData.Add(line);
                                validLines++;
                            }
                        }

                        // 定期检查内存使用情况，避免内存溢出
                        if (validLines % 10000 == 0 && validLines > 0)
                        {
                            GC.Collect(0, GCCollectionMode.Optimized);
                        }
                    }
                }

                lblStatus.Text = $"已加载 {validLines} 行有效数据" + (fileChanged ? "" : " (使用缓存)");
                lblStatus.ForeColor = Color.Green;

                // 检查缓存大小
                CheckCacheSize();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "文件加载失败";
                lblStatus.ForeColor = Color.Red;
            }
        }

        /// <summary>
        /// 验证数据行格式（内存优化版本）
        /// </summary>
        private bool ValidateDataLineOptimized(string line, int lineNumber)
        {
            try
            {
                // 使用栈分配的数组，避免堆分配
                var numbers = new int[32]; // 假设最多32个数字
                int numberCount = 0;
                int maxNumber = (int)numMaxNumber.Value;

                // 直接解析，避免Split分配
                int currentNumber = 0;
                bool hasNumber = false;
                int dashCount = 0;

                for (int i = 0; i <= line.Length; i++)
                {
                    if (i == line.Length || line[i] == '-')
                    {
                        if (hasNumber)
                        {
                            if (numberCount >= numbers.Length)
                            {
                                ShowValidationError(lineNumber, "数字数量过多", line);
                                return false;
                            }

                            if (currentNumber < 1 || currentNumber > maxNumber)
                            {
                                ShowValidationError(lineNumber, $"数字超出范围(1-{maxNumber}): {currentNumber}", line);
                                return false;
                            }

                            numbers[numberCount++] = currentNumber;
                        }
                        currentNumber = 0;
                        hasNumber = false;
                        if (i < line.Length) dashCount++;
                    }
                    else if (char.IsDigit(line[i]))
                    {
                        currentNumber = currentNumber * 10 + (line[i] - '0');
                        hasNumber = true;
                    }
                    else if (!char.IsWhiteSpace(line[i]))
                    {
                        ShowValidationError(lineNumber, $"包含无效字符: {line[i]}", line);
                        return false;
                    }
                }

                if (numberCount < 2)
                {
                    ShowValidationError(lineNumber, "至少需要2个数字", line);
                    return false;
                }

                // 检查是否按升序排列且无重复
                for (int i = 1; i < numberCount; i++)
                {
                    if (numbers[i] <= numbers[i - 1])
                    {
                        ShowValidationError(lineNumber, "数据未按升序排列或存在重复", line);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                ShowValidationError(lineNumber, $"验证失败: {ex.Message}", line);
                return false;
            }
        }

        /// <summary>
        /// 显示验证错误信息（减少重复代码）
        /// </summary>
        private void ShowValidationError(int lineNumber, string message, string line)
        {
            MessageBox.Show($"第 {lineNumber} 行{message}: {line}", "数据验证错误",
                MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        /// <summary>
        /// 验证数据行格式（保留原版本作为备用）
        /// </summary>
        private bool ValidateDataLine(string line, int lineNumber)
        {
            try
            {
                string[] parts = line.Split('-');
                if (parts.Length < 2)
                {
                    ShowValidationError(lineNumber, "至少需要2个数字", line);
                    return false;
                }

                List<int> numbers = new List<int>();
                foreach (string part in parts)
                {
                    if (!int.TryParse(part.Trim(), out int number))
                    {
                        ShowValidationError(lineNumber, $"包含无效数字: {part}", line);
                        return false;
                    }

                    if (number < 1 || number > (int)numMaxNumber.Value)
                    {
                        ShowValidationError(lineNumber, $"数字超出范围(1-{numMaxNumber.Value}): {number}", line);
                        return false;
                    }

                    numbers.Add(number);
                }

                // 检查是否按升序排列且无重复
                for (int i = 1; i < numbers.Count; i++)
                {
                    if (numbers[i] <= numbers[i - 1])
                    {
                        ShowValidationError(lineNumber, "数据未按升序排列或存在重复", line);
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                ShowValidationError(lineNumber, $"验证失败: {ex.Message}", line);
                return false;
            }
        }

        /// <summary>
        /// 开始计算按钮点击事件
        /// </summary>
        private async void btnStart_Click(object sender, EventArgs e)
        {
            if (originalData.Count == 0)
            {
                MessageBox.Show("请先选择并加载数据文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            /*
            // 验证参数
            if (numTwoCombinationMin.Value > numTwoCombinationMax.Value)
            {
                MessageBox.Show("2数组合频次范围设置错误", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            */
            if (numResultMin.Value > numResultMax.Value)
            {
                MessageBox.Show("最终结果频次范围设置错误", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 直接使用两阶段分析模式
            isProcessing = true;
            cancellationTokenSource = new CancellationTokenSource();
            UpdateUI();

            try
            {
                await ProcessTwoStageAnalysisAsync(cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
                lblStatus.Text = "计算已取消";
                lblStatus.ForeColor = Color.Orange;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "计算失败";
                lblStatus.ForeColor = Color.Red;
            }
            finally
            {
                isProcessing = false;
                progressBar.Value = 0;
                UpdateUI();
            }
        }

        /// <summary>
        /// 取消计算按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Cancel();
            }
        }

        /// <summary>
        /// 两阶段分析处理（UI响应性优化版本）
        /// </summary>
        private async Task ProcessTwoStageAnalysisAsync(CancellationToken cancellationToken)
        {
            // 清空之前的选择状态
            selectedCombinations.Clear();

            await Task.Run(async () =>
            {
                // 步骤1: 生成所有2数组合并统计频次
                UpdateStatus("正在生成2数组合...", 20);
                twoCombinationCounts = await GenerateTwoCombinationsWithProgress(cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤2: 按频次分组
                UpdateStatus("正在分析频次分布...", 60);
                GroupCombinationsByFrequency();

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤3: 更新界面显示
                UpdateStatus("正在更新显示...", 90);
                await Task.Run(() => UpdateFrequencyDisplay());

                UpdateStatus($"分析完成，共发现 {twoCombinationCounts.Count} 个不同的2数组合", 100);

            }, cancellationToken);
        }

        /// <summary>
        /// 按频次分组组合
        /// </summary>
        private void GroupCombinationsByFrequency()
        {
            frequencyCombinations.Clear();

            foreach (var kvp in twoCombinationCounts)
            {
                int frequency = kvp.Value;
                string combination = kvp.Key;

                if (!frequencyCombinations.ContainsKey(frequency))
                {
                    frequencyCombinations[frequency] = new List<string>();
                }

                frequencyCombinations[frequency].Add(combination);
            }
        }

        /// <summary>
        /// 更新频次显示（数据结构优化版本）
        /// </summary>
        private void UpdateFrequencyDisplay()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(UpdateFrequencyDisplay));
                return;
            }

            // 使用BeginUpdate/EndUpdate提高性能
            listViewFrequency.BeginUpdate();
            try
            {
                listViewFrequency.Items.Clear();

                // 使用数组排序，比LINQ更快
                var frequencies = frequencyCombinations.Keys.ToArray();
                Array.Sort(frequencies);

                // 预分配ListViewItem数组
                var items = new ListViewItem[frequencies.Length];

                for (int i = 0; i < frequencies.Length; i++)
                {
                    int frequency = frequencies[i];
                    int count = frequencyCombinations[frequency].Count;

                    var item = new ListViewItem(frequency.ToString());
                    item.SubItems.Add(count.ToString());
                    item.Tag = frequency;
                    items[i] = item;
                }

                // 批量添加项目
                listViewFrequency.Items.AddRange(items);

                lblStatus.Text = $"频次分析完成，共 {frequencies.Length} 个不同频次";
                lblStatus.ForeColor = Color.Green;
            }
            finally
            {
                listViewFrequency.EndUpdate();
            }
        }

        /// <summary>
        /// 显示选中频次的组合（数据结构优化版本）
        /// </summary>
        private void ShowCombinationsForSelectedFrequencies()
        {
            // 临时禁用事件处理，避免在重建列表时触发选择变化事件
            listViewCombinations.ItemChecked -= ListViewCombinations_ItemChecked;

            // 使用BeginUpdate/EndUpdate提高性能
            listViewCombinations.BeginUpdate();
            try
            {
                listViewCombinations.Items.Clear();

                // 使用数组替代List，提高性能
                var selectedFrequencies = new int[listViewFrequency.SelectedItems.Count];
                int freqIndex = 0;
                foreach (ListViewItem item in listViewFrequency.SelectedItems)
                {
                    selectedFrequencies[freqIndex++] = (int)item.Tag;
                }

                if (freqIndex == 0)
                {
                    return;
                }

                // 排序频次数组
                Array.Sort(selectedFrequencies, 0, freqIndex);

                // 预分配ListViewItem数组，减少重复分配
                var itemsToAdd = new List<ListViewItem>();
                int index = 1;

                for (int i = 0; i < freqIndex; i++)
                {
                    int frequency = selectedFrequencies[i];
                    if (frequencyCombinations.TryGetValue(frequency, out List<string> combinations))
                    {
                        // 使用数组排序，比LINQ更快
                        var combinationArray = combinations.ToArray();
                        Array.Sort(combinationArray, StringComparer.Ordinal);

                        foreach (string combination in combinationArray)
                        {
                            var item = new ListViewItem(index.ToString());
                            item.SubItems.Add(combination);
                            item.SubItems.Add(frequency.ToString());
                            item.Tag = new { Combination = combination, Frequency = frequency };

                            // 恢复之前的选择状态
                            item.Checked = selectedCombinations.Contains(combination);

                            itemsToAdd.Add(item);
                            index++;
                        }
                    }
                }

                // 批量添加项目，提高性能
                if (itemsToAdd.Count > 0)
                {
                    var itemArray = itemsToAdd.ToArray();
                    listViewCombinations.Items.AddRange(itemArray);
                }

                lblCombinationList.Text = $"具体组合（共 {listViewCombinations.Items.Count} 个）：";
            }
            finally
            {
                listViewCombinations.EndUpdate();

                // 重新启用事件处理
                listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;
            }

            // 更新按钮状态
            UpdateUI();
        }



        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatus(string message, int progress)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string, int>(UpdateStatus), message, progress);
                return;
            }

            lblStatus.Text = message;
            lblStatus.ForeColor = Color.Blue;
            progressBar.Value = Math.Min(progress, 100);
        }

        /// <summary>
        /// 生成所有2数组合并统计频次（带缓存和进度反馈的优化版本）
        /// </summary>
        private async Task<Dictionary<string, int>> GenerateTwoCombinationsWithProgress(CancellationToken cancellationToken)
        {
            // 生成缓存键
            string cacheKey = GenerateCacheKey(lastLoadedFilePath, lastFileHash, numMaxNumber.Value);

            // 检查缓存
            if (twoCombinationCache.TryGetValue(cacheKey, out Dictionary<string, int> cachedResult))
            {
                UpdateStatus("使用缓存的2数组合数据", 100);
                await Task.Delay(100, cancellationToken); // 短暂延迟以显示状态
                return new Dictionary<string, int>(cachedResult); // 返回副本，避免缓存被修改
            }

            var combinations = new Dictionary<string, int>();
            var sb = new StringBuilder(10); // 预分配容量，避免重复分配
            var numberBuffer = new int[32]; // 预分配数字缓冲区，假设最多32个数字

            int totalLines = originalData.Count;
            int processedLines = 0;
            int lastReportedProgress = 0;

            foreach (string line in originalData)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 优化：直接解析数字，避免Split和LINQ的开销
                int numberCount = ParseNumbersFromLine(line, numberBuffer);

                // 生成所有2数组合
                for (int i = 0; i < numberCount - 1; i++)
                {
                    for (int j = i + 1; j < numberCount; j++)
                    {
                        // 使用StringBuilder避免字符串拼接开销
                        sb.Clear();
                        sb.Append(numberBuffer[i]);
                        sb.Append('-');
                        sb.Append(numberBuffer[j]);
                        string combination = sb.ToString();

                        // 使用TryGetValue优化字典访问
                        if (combinations.TryGetValue(combination, out int count))
                        {
                            combinations[combination] = count + 1;
                        }
                        else
                        {
                            combinations[combination] = 1;
                        }
                    }
                }

                processedLines++;

                // 定期更新进度，避免UI线程过于频繁的更新
                if (processedLines % Math.Max(1, totalLines / 100) == 0)
                {
                    int currentProgress = (int)((double)processedLines / totalLines * 80) + 20; // 20-100的范围
                    if (currentProgress > lastReportedProgress)
                    {
                        lastReportedProgress = currentProgress;
                        UpdateStatus($"正在生成2数组合... ({processedLines}/{totalLines})", currentProgress);

                        // 让出控制权，保持UI响应
                        await Task.Delay(1, cancellationToken);
                    }
                }
            }

            // 将结果存入缓存
            twoCombinationCache[cacheKey] = new Dictionary<string, int>(combinations);

            return combinations;
        }

        /// <summary>
        /// 生成所有2数组合并统计频次（同步版本，保留作为备用）
        /// </summary>
        private Dictionary<string, int> GenerateTwoCombinations(CancellationToken cancellationToken)
        {
            var combinations = new Dictionary<string, int>();
            var sb = new StringBuilder(10); // 预分配容量，避免重复分配
            var numberBuffer = new int[32]; // 预分配数字缓冲区，假设最多32个数字

            foreach (string line in originalData)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 优化：直接解析数字，避免Split和LINQ的开销
                int numberCount = ParseNumbersFromLine(line, numberBuffer);

                // 生成所有2数组合
                for (int i = 0; i < numberCount - 1; i++)
                {
                    for (int j = i + 1; j < numberCount; j++)
                    {
                        // 使用StringBuilder避免字符串拼接开销
                        sb.Clear();
                        sb.Append(numberBuffer[i]);
                        sb.Append('-');
                        sb.Append(numberBuffer[j]);
                        string combination = sb.ToString();

                        // 使用TryGetValue优化字典访问
                        if (combinations.TryGetValue(combination, out int count))
                        {
                            combinations[combination] = count + 1;
                        }
                        else
                        {
                            combinations[combination] = 1;
                        }
                    }
                }
            }

            return combinations;
        }

        /// <summary>
        /// 高效解析数字行，避免Split和LINQ开销
        /// </summary>
        private int ParseNumbersFromLine(string line, int[] buffer)
        {
            int count = 0;
            int currentNumber = 0;
            bool hasNumber = false;

            for (int i = 0; i <= line.Length; i++)
            {
                if (i == line.Length || line[i] == '-')
                {
                    if (hasNumber && count < buffer.Length)
                    {
                        buffer[count++] = currentNumber;
                    }
                    currentNumber = 0;
                    hasNumber = false;
                }
                else if (char.IsDigit(line[i]))
                {
                    currentNumber = currentNumber * 10 + (line[i] - '0');
                    hasNumber = true;
                }
            }

            return count;
        }



        /// <summary>
        /// 生成磁控点组合（带缓存和进度反馈的优化版本）
        /// </summary>
        private async Task<List<string>> GenerateMagneticPointsWithProgress(List<string> validTwoCombinations, CancellationToken cancellationToken)
        {
            // 生成缓存键（基于选中的组合和参数）
            string combinationsHash = CalculateStringListHash(validTwoCombinations);
            string cacheKey = GenerateCacheKey("MagneticPoints", combinationsHash, numMagneticPoints.Value, numMaxNumber.Value);

            // 检查磁控点缓存
            if (magneticPointCache.TryGetValue(cacheKey, out Dictionary<string, int> cachedCounts))
            {
                UpdateStatus("使用缓存的磁控点数据", 70);
                await Task.Delay(100, cancellationToken);
                return cachedCounts.Keys.ToList(); // 返回磁控点列表
            }

            var magneticPoints = new List<string>();
            int magneticPointCount = (int)numMagneticPoints.Value;
            int maxNumber = (int)numMaxNumber.Value;
            int needCount = magneticPointCount - 2;

            // 预分配数组，避免重复分配
            var allNumbers = new int[maxNumber];
            for (int i = 0; i < maxNumber; i++)
            {
                allNumbers[i] = i + 1;
            }

            var remainingNumbers = new int[maxNumber - 2];
            var fullCombination = new int[magneticPointCount];
            var sb = new StringBuilder(magneticPointCount * 3); // 预估容量

            int totalCombinations = validTwoCombinations.Count;
            int processedCombinations = 0;
            int lastReportedProgress = 0;

            foreach (string twoCombination in validTwoCombinations)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 优化：直接解析数字，避免Split
                int dashIndex = twoCombination.IndexOf('-');
                int num1 = int.Parse(twoCombination.Substring(0, dashIndex));
                int num2 = int.Parse(twoCombination.Substring(dashIndex + 1));

                // 构建剩余数字数组
                int remainingCount = 0;
                for (int i = 0; i < maxNumber; i++)
                {
                    int number = allNumbers[i];
                    if (number != num1 && number != num2)
                    {
                        remainingNumbers[remainingCount++] = number;
                    }
                }

                // 生成所有可能的组合（优化版本）
                var combinations = GetCombinationsOptimized(remainingNumbers, remainingCount, needCount);

                foreach (var combination in combinations)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // 构建完整组合
                    fullCombination[0] = num1;
                    fullCombination[1] = num2;
                    Array.Copy(combination, 0, fullCombination, 2, needCount);
                    Array.Sort(fullCombination);

                    // 使用StringBuilder构建字符串
                    sb.Clear();
                    for (int i = 0; i < magneticPointCount; i++)
                    {
                        if (i > 0) sb.Append('-');
                        sb.Append(fullCombination[i]);
                    }

                    magneticPoints.Add(sb.ToString());
                }

                processedCombinations++;

                // 定期更新进度
                if (processedCombinations % Math.Max(1, totalCombinations / 50) == 0)
                {
                    int currentProgress = 30 + (int)((double)processedCombinations / totalCombinations * 40); // 30-70的范围
                    if (currentProgress > lastReportedProgress)
                    {
                        lastReportedProgress = currentProgress;
                        UpdateStatus($"正在生成磁控点组合... ({processedCombinations}/{totalCombinations})", currentProgress);

                        // 让出控制权，保持UI响应
                        await Task.Delay(1, cancellationToken);
                    }
                }
            }

            return magneticPoints;
        }

        /// <summary>
        /// 计算字符串列表的哈希值
        /// </summary>
        private string CalculateStringListHash(List<string> strings)
        {
            try
            {
                using (var md5 = System.Security.Cryptography.MD5.Create())
                {
                    var combined = string.Join("|", strings.OrderBy(s => s));
                    var bytes = Encoding.UTF8.GetBytes(combined);
                    var hash = md5.ComputeHash(bytes);
                    return Convert.ToBase64String(hash);
                }
            }
            catch
            {
                return Guid.NewGuid().ToString();
            }
        }

        /// <summary>
        /// 生成磁控点组合（同步版本，保留作为备用）
        /// </summary>
        private List<string> GenerateMagneticPoints(List<string> validTwoCombinations, CancellationToken cancellationToken)
        {
            var magneticPoints = new List<string>();
            int magneticPointCount = (int)numMagneticPoints.Value;
            int maxNumber = (int)numMaxNumber.Value;
            int needCount = magneticPointCount - 2;

            // 预分配数组，避免重复分配
            var allNumbers = new int[maxNumber];
            for (int i = 0; i < maxNumber; i++)
            {
                allNumbers[i] = i + 1;
            }

            var remainingNumbers = new int[maxNumber - 2];
            var fullCombination = new int[magneticPointCount];
            var sb = new StringBuilder(magneticPointCount * 3); // 预估容量

            foreach (string twoCombination in validTwoCombinations)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 优化：直接解析数字，避免Split
                int dashIndex = twoCombination.IndexOf('-');
                int num1 = int.Parse(twoCombination.Substring(0, dashIndex));
                int num2 = int.Parse(twoCombination.Substring(dashIndex + 1));

                // 构建剩余数字数组
                int remainingCount = 0;
                for (int i = 0; i < maxNumber; i++)
                {
                    int number = allNumbers[i];
                    if (number != num1 && number != num2)
                    {
                        remainingNumbers[remainingCount++] = number;
                    }
                }

                // 生成所有可能的组合（优化版本）
                var combinations = GetCombinationsOptimized(remainingNumbers, remainingCount, needCount);

                foreach (var combination in combinations)
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // 构建完整组合
                    fullCombination[0] = num1;
                    fullCombination[1] = num2;
                    Array.Copy(combination, 0, fullCombination, 2, needCount);
                    Array.Sort(fullCombination);

                    // 使用StringBuilder构建字符串
                    sb.Clear();
                    for (int i = 0; i < magneticPointCount; i++)
                    {
                        if (i > 0) sb.Append('-');
                        sb.Append(fullCombination[i]);
                    }

                    magneticPoints.Add(sb.ToString());
                }
            }

            return magneticPoints;
        }

        /// <summary>
        /// 获取组合（C(n,r)）- 高性能优化版本
        /// </summary>
        private IEnumerable<int[]> GetCombinationsOptimized(int[] array, int arrayLength, int length)
        {
            if (length == 0)
            {
                yield return new int[0];
                yield break;
            }

            if (length == 1)
            {
                for (int i = 0; i < arrayLength; i++)
                {
                    yield return new int[] { array[i] };
                }
                yield break;
            }

            // 使用迭代方式生成组合，避免递归开销
            var indices = new int[length];
            for (int i = 0; i < length; i++)
            {
                indices[i] = i;
            }

            var result = new int[length];
            do
            {
                // 构建当前组合
                for (int i = 0; i < length; i++)
                {
                    result[i] = array[indices[i]];
                }

                // 返回当前组合的副本
                var combination = new int[length];
                Array.Copy(result, combination, length);
                yield return combination;

            } while (NextCombination(indices, arrayLength, length));
        }

        /// <summary>
        /// 生成下一个组合的索引（字典序）
        /// </summary>
        private bool NextCombination(int[] indices, int n, int k)
        {
            for (int i = k - 1; i >= 0; i--)
            {
                if (indices[i] < n - k + i)
                {
                    indices[i]++;
                    for (int j = i + 1; j < k; j++)
                    {
                        indices[j] = indices[j - 1] + 1;
                    }
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取组合（C(n,r)）- 保留原版本作为备用
        /// </summary>
        private IEnumerable<List<int>> GetCombinations(List<int> list, int length)
        {
            if (length == 0)
                yield return new List<int>();
            else if (length == 1)
            {
                foreach (var item in list)
                    yield return new List<int> { item };
            }
            else
            {
                for (int i = 0; i <= list.Count - length; i++)
                {
                    var head = list[i];
                    var tail = list.Skip(i + 1).ToList();

                    foreach (var combination in GetCombinations(tail, length - 1))
                    {
                        var result = new List<int> { head };
                        result.AddRange(combination);
                        yield return result;
                    }
                }
            }
        }

        /// <summary>
        /// 统计磁控点频次（带进度反馈的优化版本）
        /// </summary>
        private async Task<Dictionary<string, int>> CountMagneticPointsWithProgress(List<string> magneticPoints, CancellationToken cancellationToken)
        {
            // 根据数据量选择不同的处理策略
            if (magneticPoints.Count > 1000000)
            {
                return await CountMagneticPointsParallelWithProgress(magneticPoints, cancellationToken);
            }

            var counts = new Dictionary<string, int>(magneticPoints.Count / 4); // 预估容量
            int totalPoints = magneticPoints.Count;
            int processedPoints = 0;
            int lastReportedProgress = 0;

            foreach (string point in magneticPoints)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 使用TryGetValue优化字典访问
                if (counts.TryGetValue(point, out int count))
                {
                    counts[point] = count + 1;
                }
                else
                {
                    counts[point] = 1;
                }

                processedPoints++;

                // 定期更新进度
                if (processedPoints % Math.Max(1, totalPoints / 100) == 0)
                {
                    int currentProgress = 70 + (int)((double)processedPoints / totalPoints * 15); // 70-85的范围
                    if (currentProgress > lastReportedProgress)
                    {
                        lastReportedProgress = currentProgress;
                        UpdateStatus($"正在统计磁控点频次... ({processedPoints}/{totalPoints})", currentProgress);

                        // 让出控制权，保持UI响应
                        await Task.Delay(1, cancellationToken);
                    }
                }
            }

            return counts;
        }

        /// <summary>
        /// 统计磁控点频次（同步版本，保留作为备用）
        /// </summary>
        private Dictionary<string, int> CountMagneticPoints(List<string> magneticPoints, CancellationToken cancellationToken)
        {
            // 根据数据量选择不同的处理策略
            if (magneticPoints.Count > 1000000)
            {
                return CountMagneticPointsParallel(magneticPoints, cancellationToken);
            }

            var counts = new Dictionary<string, int>(magneticPoints.Count / 4); // 预估容量

            foreach (string point in magneticPoints)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 使用TryGetValue优化字典访问
                if (counts.TryGetValue(point, out int count))
                {
                    counts[point] = count + 1;
                }
                else
                {
                    counts[point] = 1;
                }
            }

            return counts;
        }

        /// <summary>
        /// 并行统计磁控点频次（带进度反馈，用于大数据量）
        /// </summary>
        private async Task<Dictionary<string, int>> CountMagneticPointsParallelWithProgress(List<string> magneticPoints, CancellationToken cancellationToken)
        {
            var concurrentCounts = new ConcurrentDictionary<string, int>();
            var processedCount = 0;
            var totalCount = magneticPoints.Count;
            var lastReportedProgress = 0;

            var parallelOptions = new ParallelOptions
            {
                CancellationToken = cancellationToken,
                MaxDegreeOfParallelism = Environment.ProcessorCount
            };

            try
            {
                await Task.Run(() =>
                {
                    Parallel.ForEach(magneticPoints, parallelOptions, point =>
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        concurrentCounts.AddOrUpdate(point, 1, (key, oldValue) => oldValue + 1);

                        var currentProcessed = Interlocked.Increment(ref processedCount);

                        // 定期更新进度（减少锁竞争）
                        if (currentProcessed % Math.Max(1, totalCount / 100) == 0)
                        {
                            var currentProgress = 70 + (int)((double)currentProcessed / totalCount * 15); // 70-85的范围
                            if (currentProgress > lastReportedProgress)
                            {
                                Interlocked.Exchange(ref lastReportedProgress, currentProgress);
                                UpdateStatus($"正在并行统计磁控点频次... ({currentProcessed}/{totalCount})", currentProgress);
                            }
                        }
                    });
                }, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                throw; // 重新抛出取消异常
            }

            // 转换为普通字典
            return new Dictionary<string, int>(concurrentCounts);
        }

        /// <summary>
        /// 并行统计磁控点频次（同步版本，保留作为备用）
        /// </summary>
        private Dictionary<string, int> CountMagneticPointsParallel(List<string> magneticPoints, CancellationToken cancellationToken)
        {
            var concurrentCounts = new ConcurrentDictionary<string, int>();

            var parallelOptions = new ParallelOptions
            {
                CancellationToken = cancellationToken,
                MaxDegreeOfParallelism = Environment.ProcessorCount
            };

            try
            {
                Parallel.ForEach(magneticPoints, parallelOptions, point =>
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    concurrentCounts.AddOrUpdate(point, 1, (key, oldValue) => oldValue + 1);
                });
            }
            catch (OperationCanceledException)
            {
                throw; // 重新抛出取消异常
            }

            // 转换为普通字典
            return new Dictionary<string, int>(concurrentCounts);
        }









        /// <summary>
        /// 导出选中组合按钮点击事件
        /// </summary>
        private async void btnExportSelected_Click(object sender, EventArgs e)
        {
            if (selectedCombinations.Count == 0)
            {
                MessageBox.Show("请先选择要导出的组合", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 确认是否继续计算磁控点
            var result = MessageBox.Show($"将基于您选中的 {selectedCombinations.Count} 个2数组合计算磁控点分布。\n\n这可能需要一些时间，是否继续？",
                "确认计算", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result != DialogResult.Yes) return;

            isProcessing = true;
            cancellationTokenSource = new CancellationTokenSource();
            UpdateUI();

            try
            {
                await ProcessSelectedCombinationsAsync(cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
                lblStatus.Text = "计算已取消";
                lblStatus.ForeColor = Color.Orange;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算过程中发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "计算失败";
                lblStatus.ForeColor = Color.Red;
            }
            finally
            {
                isProcessing = false;
                progressBar.Value = 0;
                UpdateUI();
            }
        }

        /// <summary>
        /// 处理选中组合的磁控点计算（UI响应性优化版本）
        /// </summary>
        private async Task ProcessSelectedCombinationsAsync(CancellationToken cancellationToken)
        {
            await Task.Run(async () =>
            {
                // 步骤1: 将选中的组合转换为有效组合列表
                UpdateStatus("正在准备选中的2数组合...", 10);
                var validTwoCombinations = selectedCombinations.ToList();

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤2: 生成磁控点组合
                UpdateStatus("正在生成磁控点组合...", 30);
                var magneticPoints = await GenerateMagneticPointsWithProgress(validTwoCombinations, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤3: 统计磁控点频次
                UpdateStatus("正在统计磁控点频次...", 70);
                var magneticPointCounts = await CountMagneticPointsWithProgress(magneticPoints, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤4: 筛选最终结果（基于选中的组合，并按频次范围筛选）
                UpdateStatus("正在重新统计频次并筛选结果...", 85);
                var filteredResults = await FilterResultsBySelectedCombinationsWithProgress(magneticPointCounts, validTwoCombinations, cancellationToken);

                cancellationToken.ThrowIfCancellationRequested();

                // 步骤5: 导出结果
                UpdateStatus("正在导出结果...", 95);
                await Task.Run(() => ExportMagneticPointResults(filteredResults));

                UpdateStatus($"计算完成，共生成 {filteredResults.Count} 个磁控点结果", 100);

            }, cancellationToken);
        }

        /// <summary>
        /// 基于选中组合筛选磁控点结果（带进度反馈的优化版本）
        /// </summary>
        private async Task<List<string>> FilterResultsBySelectedCombinationsWithProgress(Dictionary<string, int> magneticPointCounts,
            List<string> selectedTwoCombinations, CancellationToken cancellationToken)
        {
            // 根据数据量选择不同的处理策略
            if (magneticPointCounts.Count > 500000) // 超大数据量时使用并行处理
            {
                return await FilterResultsBySelectedCombinationsParallelWithProgress(magneticPointCounts, selectedTwoCombinations, cancellationToken);
            }
            else if (magneticPointCounts.Count > 100000) // 大数据量时使用流式处理
            {
                return await FilterResultsBySelectedCombinationsLargeDataWithProgress(magneticPointCounts, selectedTwoCombinations, cancellationToken);
            }

            var selectedCombinationsSet = new HashSet<string>(selectedTwoCombinations);
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;

            // 使用字典直接统计，避免创建大量重复字符串
            var newMagneticPointCounts = new Dictionary<string, int>();

            int totalItems = magneticPointCounts.Count;
            int processedItems = 0;
            int lastReportedProgress = 0;

            // 一次遍历完成筛选和统计
            foreach (var kvp in magneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查磁控点是否包含至少一个选中的2数组合
                if (ContainsSelectedTwoCombinationOptimized(kvp.Key, selectedCombinationsSet))
                {
                    // 直接累加频次，避免创建重复字符串
                    if (newMagneticPointCounts.TryGetValue(kvp.Key, out int existingCount))
                    {
                        newMagneticPointCounts[kvp.Key] = existingCount + kvp.Value;
                    }
                    else
                    {
                        newMagneticPointCounts[kvp.Key] = kvp.Value;
                    }
                }

                processedItems++;

                // 定期更新进度
                if (processedItems % Math.Max(1, totalItems / 100) == 0)
                {
                    int currentProgress = 85 + (int)((double)processedItems / totalItems * 10); // 85-95的范围
                    if (currentProgress > lastReportedProgress)
                    {
                        lastReportedProgress = currentProgress;
                        UpdateStatus($"正在筛选结果... ({processedItems}/{totalItems})", currentProgress);

                        // 让出控制权，保持UI响应
                        await Task.Delay(1, cancellationToken);
                    }
                }
            }

            // 直接筛选并返回结果，无需额外的去重步骤
            var results = new List<string>();
            foreach (var kvp in newMagneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查频次是否在设定范围内
                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 基于选中组合筛选磁控点结果（同步版本，保留作为备用）
        /// </summary>
        private List<string> FilterResultsBySelectedCombinations(Dictionary<string, int> magneticPointCounts,
            List<string> selectedTwoCombinations, CancellationToken cancellationToken)
        {
            // 根据数据量选择不同的处理策略
            if (magneticPointCounts.Count > 500000) // 超大数据量时使用并行处理
            {
                return FilterResultsBySelectedCombinationsParallel(magneticPointCounts, selectedTwoCombinations, cancellationToken);
            }
            else if (magneticPointCounts.Count > 100000) // 大数据量时使用流式处理
            {
                return FilterResultsBySelectedCombinationsLargeData(magneticPointCounts, selectedTwoCombinations, cancellationToken);
            }

            var selectedCombinationsSet = new HashSet<string>(selectedTwoCombinations);
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;

            // 使用字典直接统计，避免创建大量重复字符串
            var newMagneticPointCounts = new Dictionary<string, int>();

            // 一次遍历完成筛选和统计
            foreach (var kvp in magneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查磁控点是否包含至少一个选中的2数组合
                if (ContainsSelectedTwoCombinationOptimized(kvp.Key, selectedCombinationsSet))
                {
                    // 直接累加频次，避免创建重复字符串
                    if (newMagneticPointCounts.TryGetValue(kvp.Key, out int existingCount))
                    {
                        newMagneticPointCounts[kvp.Key] = existingCount + kvp.Value;
                    }
                    else
                    {
                        newMagneticPointCounts[kvp.Key] = kvp.Value;
                    }
                }
            }

            // 直接筛选并返回结果，无需额外的去重步骤
            var results = new List<string>();
            foreach (var kvp in newMagneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查频次是否在设定范围内
                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 并行筛选磁控点结果（带进度反馈，用于超大数据量）
        /// </summary>
        private async Task<List<string>> FilterResultsBySelectedCombinationsParallelWithProgress(Dictionary<string, int> magneticPointCounts,
            List<string> selectedTwoCombinations, CancellationToken cancellationToken)
        {
            var selectedCombinationsSet = new HashSet<string>(selectedTwoCombinations);
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;

            // 使用并发字典支持多线程访问
            var newMagneticPointCounts = new ConcurrentDictionary<string, int>();
            var processedCount = 0;
            var totalCount = magneticPointCounts.Count;
            var lastReportedProgress = 0;

            var parallelOptions = new ParallelOptions
            {
                CancellationToken = cancellationToken,
                MaxDegreeOfParallelism = Environment.ProcessorCount
            };

            try
            {
                await Task.Run(() =>
                {
                    Parallel.ForEach(magneticPointCounts, parallelOptions, kvp =>
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        // 检查磁控点是否包含至少一个选中的2数组合
                        if (ContainsSelectedTwoCombinationOptimized(kvp.Key, selectedCombinationsSet))
                        {
                            newMagneticPointCounts.AddOrUpdate(kvp.Key, kvp.Value, (key, existingValue) => existingValue + kvp.Value);
                        }

                        var currentProcessed = Interlocked.Increment(ref processedCount);

                        // 定期更新进度（减少锁竞争）
                        if (currentProcessed % Math.Max(1, totalCount / 100) == 0)
                        {
                            var currentProgress = 85 + (int)((double)currentProcessed / totalCount * 10); // 85-95的范围
                            if (currentProgress > lastReportedProgress)
                            {
                                Interlocked.Exchange(ref lastReportedProgress, currentProgress);
                                UpdateStatus($"正在并行筛选结果... ({currentProcessed}/{totalCount})", currentProgress);
                            }
                        }
                    });
                }, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                throw; // 重新抛出取消异常
            }

            // 筛选结果
            var results = new List<string>();
            foreach (var kvp in newMagneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 大数据量流式筛选磁控点结果（带进度反馈）
        /// </summary>
        private async Task<List<string>> FilterResultsBySelectedCombinationsLargeDataWithProgress(Dictionary<string, int> magneticPointCounts,
            List<string> selectedTwoCombinations, CancellationToken cancellationToken)
        {
            var selectedCombinationsSet = new HashSet<string>(selectedTwoCombinations);
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;

            // 使用更大的初始容量减少重新分配
            var newMagneticPointCounts = new Dictionary<string, int>(magneticPointCounts.Count / 4);
            var results = new List<string>();

            // 分批处理，每批处理10000个项目
            const int batchSize = 10000;
            int processedCount = 0;
            int totalCount = magneticPointCounts.Count;
            int lastReportedProgress = 0;

            foreach (var kvp in magneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查磁控点是否包含至少一个选中的2数组合
                if (ContainsSelectedTwoCombinationOptimized(kvp.Key, selectedCombinationsSet))
                {
                    // 直接累加频次
                    if (newMagneticPointCounts.TryGetValue(kvp.Key, out int existingCount))
                    {
                        newMagneticPointCounts[kvp.Key] = existingCount + kvp.Value;
                    }
                    else
                    {
                        newMagneticPointCounts[kvp.Key] = kvp.Value;
                    }
                }

                processedCount++;

                // 定期更新进度和清理内存
                if (processedCount % batchSize == 0)
                {
                    int currentProgress = 85 + (int)((double)processedCount / totalCount * 10); // 85-95的范围
                    if (currentProgress > lastReportedProgress)
                    {
                        lastReportedProgress = currentProgress;
                        UpdateStatus($"正在流式筛选结果... ({processedCount}/{totalCount})", currentProgress);

                        // 让出控制权，保持UI响应
                        await Task.Delay(1, cancellationToken);
                    }

                    // 将符合条件的结果移到结果列表，并从临时字典中移除
                    var toRemove = new List<string>();
                    foreach (var tempKvp in newMagneticPointCounts)
                    {
                        if (tempKvp.Value >= minCount && tempKvp.Value <= maxCount)
                        {
                            results.Add(tempKvp.Key);
                            toRemove.Add(tempKvp.Key);
                        }
                    }

                    // 移除已处理的项目以释放内存
                    foreach (var key in toRemove)
                    {
                        newMagneticPointCounts.Remove(key);
                    }

                    // 强制垃圾回收（仅在大数据量时）
                    if (processedCount % (batchSize * 10) == 0)
                    {
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                    }
                }
            }

            // 处理剩余的数据
            foreach (var kvp in newMagneticPointCounts)
            {
                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 大数据量时的流式处理版本，避免内存溢出
        /// </summary>
        private List<string> FilterResultsBySelectedCombinationsLargeData(Dictionary<string, int> magneticPointCounts,
            List<string> selectedTwoCombinations, CancellationToken cancellationToken)
        {
            var selectedCombinationsSet = new HashSet<string>(selectedTwoCombinations);
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;

            // 使用更大的初始容量减少重新分配
            var newMagneticPointCounts = new Dictionary<string, int>(magneticPointCounts.Count / 4);
            var results = new List<string>();

            // 分批处理，每批处理10000个项目
            const int batchSize = 10000;
            int processedCount = 0;

            foreach (var kvp in magneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                // 检查磁控点是否包含至少一个选中的2数组合
                if (ContainsSelectedTwoCombinationOptimized(kvp.Key, selectedCombinationsSet))
                {
                    // 直接累加频次
                    if (newMagneticPointCounts.TryGetValue(kvp.Key, out int existingCount))
                    {
                        newMagneticPointCounts[kvp.Key] = existingCount + kvp.Value;
                    }
                    else
                    {
                        newMagneticPointCounts[kvp.Key] = kvp.Value;
                    }
                }

                processedCount++;

                // 每处理一批数据，检查是否需要进行中间筛选以释放内存
                if (processedCount % batchSize == 0)
                {
                    // 将符合条件的结果移到结果列表，并从临时字典中移除
                    var toRemove = new List<string>();
                    foreach (var tempKvp in newMagneticPointCounts)
                    {
                        if (tempKvp.Value >= minCount && tempKvp.Value <= maxCount)
                        {
                            results.Add(tempKvp.Key);
                            toRemove.Add(tempKvp.Key);
                        }
                    }

                    // 移除已处理的项目以释放内存
                    foreach (var key in toRemove)
                    {
                        newMagneticPointCounts.Remove(key);
                    }

                    // 强制垃圾回收（仅在大数据量时）
                    if (processedCount % (batchSize * 10) == 0)
                    {
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                    }
                }
            }

            // 处理剩余的数据
            foreach (var kvp in newMagneticPointCounts)
            {
                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 超大数据量时的并行处理版本
        /// </summary>
        private List<string> FilterResultsBySelectedCombinationsParallel(Dictionary<string, int> magneticPointCounts,
            List<string> selectedTwoCombinations, CancellationToken cancellationToken)
        {
            var selectedCombinationsSet = new HashSet<string>(selectedTwoCombinations);
            int minCount = (int)numResultMin.Value;
            int maxCount = (int)numResultMax.Value;

            // 使用并发字典支持多线程访问
            var newMagneticPointCounts = new ConcurrentDictionary<string, int>();

            // 并行处理磁控点筛选和统计
            var parallelOptions = new ParallelOptions
            {
                CancellationToken = cancellationToken,
                MaxDegreeOfParallelism = Environment.ProcessorCount // 限制并行度避免过度占用资源
            };

            try
            {
                Parallel.ForEach(magneticPointCounts, parallelOptions, kvp =>
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // 检查磁控点是否包含至少一个选中的2数组合
                    if (ContainsSelectedTwoCombinationOptimized(kvp.Key, selectedCombinationsSet))
                    {
                        // 使用AddOrUpdate进行线程安全的累加
                        newMagneticPointCounts.AddOrUpdate(kvp.Key, kvp.Value, (key, existingValue) => existingValue + kvp.Value);
                    }
                });
            }
            catch (OperationCanceledException)
            {
                throw; // 重新抛出取消异常
            }

            // 筛选结果
            var results = new List<string>();
            foreach (var kvp in newMagneticPointCounts)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (kvp.Value >= minCount && kvp.Value <= maxCount)
                {
                    results.Add(kvp.Key);
                }
            }

            return results;
        }

        /// <summary>
        /// 检查磁控点是否包含选中的2数组合（.NET Framework兼容优化版本）
        /// </summary>
        private bool ContainsSelectedTwoCombinationOptimized(string magneticPoint, HashSet<string> selectedCombinations)
        {
            // 使用数组替代Span，兼容.NET Framework
            var numbers = new int[16]; // 假设最多16个数字，足够处理大部分情况
            int numberCount = 0;

            // 手动解析数字，避免Split和LINQ的开销
            int currentNumber = 0;
            bool hasNumber = false;

            for (int i = 0; i <= magneticPoint.Length; i++)
            {
                if (i == magneticPoint.Length || magneticPoint[i] == '-')
                {
                    if (hasNumber && numberCount < numbers.Length)
                    {
                        numbers[numberCount++] = currentNumber;
                    }
                    currentNumber = 0;
                    hasNumber = false;
                }
                else if (char.IsDigit(magneticPoint[i]))
                {
                    currentNumber = currentNumber * 10 + (magneticPoint[i] - '0');
                    hasNumber = true;
                }
            }

            // 检查所有2数组合，使用StringBuilder减少字符串分配
            var sb = new StringBuilder(10); // 预分配容量
            for (int i = 0; i < numberCount - 1; i++)
            {
                for (int j = i + 1; j < numberCount; j++)
                {
                    sb.Clear();
                    sb.Append(numbers[i]);
                    sb.Append('-');
                    sb.Append(numbers[j]);

                    if (selectedCombinations.Contains(sb.ToString()))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 高性能版本：使用缓存的字符串避免重复构造（适用于频繁调用的场景）
        /// </summary>
        private bool ContainsSelectedTwoCombinationCached(string magneticPoint, HashSet<string> selectedCombinations)
        {
            // 对于小数据量，直接使用Split可能更快（JIT优化）
            if (magneticPoint.Length < 20)
            {
                return ContainsSelectedTwoCombination(magneticPoint, selectedCombinations);
            }

            // 对于大数据量，使用优化版本
            return ContainsSelectedTwoCombinationOptimized(magneticPoint, selectedCombinations);
        }

        /// <summary>
        /// 检查磁控点是否包含选中的2数组合（保留原版本作为备用）
        /// </summary>
        private bool ContainsSelectedTwoCombination(string magneticPoint, HashSet<string> selectedCombinations)
        {
            string[] parts = magneticPoint.Split('-');
            List<int> numbers = parts.Select(int.Parse).ToList();

            // 检查所有2数组合
            for (int i = 0; i < numbers.Count - 1; i++)
            {
                for (int j = i + 1; j < numbers.Count; j++)
                {
                    string combination = $"{numbers[i]}-{numbers[j]}";
                    if (selectedCombinations.Contains(combination))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 导出磁控点结果（内存优化版本）
        /// </summary>
        private void ExportMagneticPointResults(List<string> results)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<List<string>>(ExportMagneticPointResults), results);
                return;
            }

            if (results.Count == 0)
            {
                MessageBox.Show("基于选中的2数组合未生成任何磁控点结果", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            saveFileDialog.FileName = $"基于选中组合的磁控点结果_{DateTime.Now:yyyyMMdd_HHmmss}.txt";

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    // 使用流式写入，避免构建大字符串
                    using (var writer = new StreamWriter(saveFileDialog.FileName, false, Encoding.UTF8))
                    {
                        // 按数字顺序排序导出（内存优化版本）
                        var sortedResults = results.OrderBy(r => ParseResultForSorting(r), new ArrayComparer()).ToList();

                        foreach (string result in sortedResults)
                        {
                            writer.WriteLine(result);
                        }
                    }

                    MessageBox.Show($"磁控点结果已成功导出到:\n{saveFileDialog.FileName}\n\n共导出 {results.Count} 个磁控点组合",
                        "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    lblStatus.Text = $"已导出 {results.Count} 个磁控点结果到: {Path.GetFileName(saveFileDialog.FileName)}";
                    lblStatus.ForeColor = Color.Green;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    lblStatus.Text = "导出失败";
                    lblStatus.ForeColor = Color.Red;
                }
            }
        }

        /// <summary>
        /// 解析结果字符串用于排序（避免重复Split操作）
        /// </summary>
        private int[] ParseResultForSorting(string result)
        {
            var numbers = new List<int>();
            int currentNumber = 0;
            bool hasNumber = false;

            for (int i = 0; i <= result.Length; i++)
            {
                if (i == result.Length || result[i] == '-')
                {
                    if (hasNumber)
                    {
                        numbers.Add(currentNumber);
                    }
                    currentNumber = 0;
                    hasNumber = false;
                }
                else if (char.IsDigit(result[i]))
                {
                    currentNumber = currentNumber * 10 + (result[i] - '0');
                    hasNumber = true;
                }
            }

            return numbers.ToArray();
        }

        /// <summary>
        /// 获取选中的组合
        /// </summary>
        private List<dynamic> GetSelectedCombinations()
        {
            var result = new List<dynamic>();

            // 从全局选择集合中获取组合信息
            foreach (string combination in selectedCombinations)
            {
                if (twoCombinationCounts.ContainsKey(combination))
                {
                    result.Add(new { Combination = combination, Frequency = twoCombinationCounts[combination] });
                }
            }

            return result;
        }

        /// <summary>
        /// 全选按钮点击事件
        /// </summary>
        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            // 临时禁用事件处理
            listViewCombinations.ItemChecked -= ListViewCombinations_ItemChecked;

            foreach (ListViewItem item in listViewCombinations.Items)
            {
                item.Checked = true;
                var itemData = (dynamic)item.Tag;
                selectedCombinations.Add(itemData.Combination);
            }

            // 重新启用事件处理
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;

            UpdateExportButtonState();
        }

        /// <summary>
        /// 清空选择按钮点击事件
        /// </summary>
        private void btnClearSelection_Click(object sender, EventArgs e)
        {
            // 临时禁用事件处理
            listViewCombinations.ItemChecked -= ListViewCombinations_ItemChecked;

            foreach (ListViewItem item in listViewCombinations.Items)
            {
                item.Checked = false;
                var itemData = (dynamic)item.Tag;
                selectedCombinations.Remove(itemData.Combination);
            }

            // 重新启用事件处理
            listViewCombinations.ItemChecked += ListViewCombinations_ItemChecked;

            UpdateExportButtonState();
        }
    }

    /// <summary>
    /// 数组比较器，用于正确排序磁控点组合
    /// </summary>
    public class ArrayComparer : IComparer<int[]>
    {
        public int Compare(int[] x, int[] y)
        {
            if (x == null && y == null) return 0;
            if (x == null) return -1;
            if (y == null) return 1;

            int minLength = Math.Min(x.Length, y.Length);
            for (int i = 0; i < minLength; i++)
            {
                int comparison = x[i].CompareTo(y[i]);
                if (comparison != 0) return comparison;
            }

            return x.Length.CompareTo(y.Length);
        }
    }
}
